app: agent-wrapper
nameOverride: agent-wrapper
fullnameOverride: agent-wrapper
image:
  registry: "205744758777.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-agent-wrapper"
  tag: "1576586"
  pullPolicy: Always
replicaCount: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 1
        periodSeconds: 60
podDisruptionBudget:
  maxUnavailable: 1 
service:
  type: ClusterIP
  port: 80
ports: |
  - containerPort: 8080
    name: management
    protocol: TCP
  - containerPort: 8888
    name: jmx
    protocol: TCP
podLabels:
  # Allow datadog to inject variables required for APM
  admission.datadoghq.com/enabled: "true"
env:
  DD_PROFILING_ENABLED: "false"
  DD_SERVICE: agent-wrapper
  JAVA_OPTS: -Xms10G -Xmx10G -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.port=8888 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.local.only=false -Djava.rmi.server.hostname=localhost -Djava.security.egd=file:/dev/urandom -XX:+ExitOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heap-dump.hprof
  JAVA_TOOL_OPTIONS: -javaagent:/datadog/dd-java-agent.jar
  LOGGING_LEVEL_COM_DATADOG_PROFILING_UPLOADER_PROFILEUPLOADER: FATAL
  LOGGING_LEVEL_DATADOG_TRACE_AGENT_COMMON_WRITER_DDAGENT_DDAGENTAPI: FATAL
  SCAN_TIMEOUT: 30m
  SCANCONFIG_KEYSTOREPATH: /certs/keystore/keystore.jks
  SCANCONFIG_TRUSTSTOREPATH: /certs/keystore/truststore.jks
  SCANCONFIG_URL: https://scanconfig-service
  SRCCLR_ISHTTP: "false"
  SRCCLR_URL: http://platform-backend
  UNPACK_RECURSIVELY: "true"
valueFrom:
  - name: SCANCONFIG_KEYSTOREPASSWORD
    valueFrom:
      secretKeyRef:
        name: agent-wrapper-secrets
        key: keystore-password
  - name: SCANCONFIG_TRUSTSTOREPASSWORD
    valueFrom:
      secretKeyRef:
        name: agent-wrapper-secrets
        key: truststore-password
  - name: SRCCLR_KEY
    valueFrom:
      secretKeyRef:
        name: agent-wrapper-secrets
        key: srcclr-key
readinessProbe: |
  failureThreshold: 3
  httpGet:
    path: "/actuator/health"
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 30
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
resources:
  limits:
    memory: 6G
  requests:
    memory: 4G
volumes:
  - name: agent-wrapper-secrets
    secret:
      secretName: agent-wrapper-secrets
      defaultMode: 420
  - name: agent-wrapper-certs
    secret:
      secretName: agent-wrapper-certs
      defaultMode: 420
  - name: agent-wrapper-tmp
    ephemeral:
      volumeClaimTemplate:
        metadata:
          labels:
            type: agent-wrapper-tmp-volume
        spec:
          accessModes: ["ReadWriteOnce"]
          storageClassName: gp3-retain
          resources:
            requests:
              storage: 5Gi
volumeMounts:
  - name: agent-wrapper-secrets
    mountPath: "/secrets"
    readOnly: true
  - name: agent-wrapper-certs
    mountPath: "/certs/keystore"
    readOnly: true
  - name: agent-wrapper-tmp
    mountPath: "/tmp"
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/agent-wrapper-dev-dyn-tachyon-serviceaccount
  name: agent-wrapper
automountServiceAccountToken: true
