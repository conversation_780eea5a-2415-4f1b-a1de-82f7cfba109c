{{- range .Values.external_secrets }}
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: {{ .name }}
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-manager
    kind: ClusterSecretStore
  target:
    name: {{ .name }}
    creationPolicy: Owner
  data:
{{- range .data }}
    - secretKey: {{ .name }}
      remoteRef:
        key: {{ .key }}
        property: {{ .property }}
{{- end }}
---
{{- end }}
