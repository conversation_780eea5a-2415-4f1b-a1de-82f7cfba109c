# AWS Account: veracode-platform-nonprod
# Location: US

image:
  tag: "ATL-3803-eeecc0fd"
external_secrets:
- name: agent-wrapper-secrets
  data:
    - name: keystore-password
      key: stage/shared/agent-wrapper-secrets
      property: keystore-password
    - name: srcclr-key
      key: stage/shared/agent-wrapper-secrets
      property: srcclr-key
    - name: truststore-password
      key: stage/shared/agent-wrapper-secrets
      property: truststore-password
- name: agent-wrapper-certs
  data:
    - name: keystore.jks
      key: stage/shared/agent-wrapper-certs
      property: keystore.jks
    - name: truststore.jks
      key: stage/shared/agent-wrapper-certs
      property: truststore.jks
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/agent-wrapper-stage-dyn-tachyon-serviceaccount
env:
  DD_TAGS: env:stage stack_name:stage-dyn-tachyon
  ROLE_ARN: arn:aws:iam::************:role/vosp-S3-role-stage-132
  ROLE_SESSION_NAME: agent-wrapper-session-qa
  SPRING_PROFILES_ACTIVE: qa
  SQS_QUEUE: stage-132-upload-scan-config
  SRCCLR_BUCKET: agent-wrapper-evidence-stage-132
  SRCCLR_QUEUE: stage-132-upload-scan-evidence